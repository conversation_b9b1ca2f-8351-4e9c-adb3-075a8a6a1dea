/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import { useCallback, useEffect, useMemo, useReducer } from 'react';
import { spawnSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
/**
 * Calculate code point length (Unicode-aware)
 */
function cpLen(str) {
    return [...str].length;
}
/**
 * Get substring by code points
 */
function cpSubstring(str, start, end) {
    const codePoints = [...str];
    return codePoints.slice(start, end).join('');
}
/**
 * Calculate initial cursor position
 */
function calculateInitialCursorPosition(lines, offset) {
    let currentOffset = 0;
    for (let row = 0; row < lines.length; row++) {
        const line = lines[row];
        if (line === undefined)
            continue;
        const lineLength = cpLen(line);
        if (currentOffset + lineLength >= offset) {
            return [row, offset - currentOffset];
        }
        currentOffset += lineLength + 1; // +1 for newline
    }
    // If offset is beyond text, place at end
    const lastRow = lines.length - 1;
    return [lastRow, cpLen(lines[lastRow] || '')];
}
/**
 * Convert offset to logical position
 */
function offsetToLogicalPos(text, offset) {
    const lines = text.split('\n');
    return calculateInitialCursorPosition(lines, offset);
}
/**
 * Convert logical position to offset
 */
function logicalPosToOffset(lines, row, col) {
    let offset = 0;
    for (let i = 0; i < row && i < lines.length; i++) {
        const line = lines[i];
        if (line !== undefined) {
            offset += cpLen(line) + 1; // +1 for newline
        }
    }
    offset += Math.min(col, cpLen(lines[row] || ''));
    return offset;
}
/**
 * Push current state to undo stack
 */
function pushUndo(state) {
    const undoEntry = {
        lines: [...state.lines],
        cursorRow: state.cursorRow,
        cursorCol: state.cursorCol,
    };
    return {
        ...state,
        undoStack: [...state.undoStack.slice(-19), undoEntry], // Keep last 20 entries
        redoStack: [], // Clear redo stack on new action
    };
}
/**
 * Text buffer reducer
 */
export function textBufferReducer(state, action) {
    const currentLine = (r) => state.lines[r] ?? '';
    const currentLineLen = (r) => cpLen(currentLine(r));
    switch (action.type) {
        case 'set_text': {
            let nextState = state;
            if (action.pushToUndo !== false) {
                nextState = pushUndo(state);
            }
            const newContentLines = action.payload.replace(/\r\n?/g, '\n').split('\n');
            const lines = newContentLines.length === 0 ? [''] : newContentLines;
            const lastNewLineIndex = lines.length - 1;
            return {
                ...nextState,
                lines,
                cursorRow: lastNewLineIndex,
                cursorCol: cpLen(lines[lastNewLineIndex] ?? ''),
                preferredCol: null,
            };
        }
        case 'insert': {
            const nextState = pushUndo(state);
            const lines = [...state.lines];
            const currentLineText = lines[state.cursorRow] || '';
            const codePoints = [...currentLineText];
            codePoints.splice(state.cursorCol, 0, ...action.payload);
            lines[state.cursorRow] = codePoints.join('');
            return {
                ...nextState,
                lines,
                cursorCol: state.cursorCol + cpLen(action.payload),
                preferredCol: null,
            };
        }
        case 'newline': {
            const nextState = pushUndo(state);
            const lines = [...state.lines];
            const currentLineText = lines[state.cursorRow] || '';
            const codePoints = [...currentLineText];
            const beforeCursor = codePoints.slice(0, state.cursorCol).join('');
            const afterCursor = codePoints.slice(state.cursorCol).join('');
            lines[state.cursorRow] = beforeCursor;
            lines.splice(state.cursorRow + 1, 0, afterCursor);
            return {
                ...nextState,
                lines,
                cursorRow: state.cursorRow + 1,
                cursorCol: 0,
                preferredCol: null,
            };
        }
        case 'backspace': {
            if (state.cursorCol > 0) {
                const nextState = pushUndo(state);
                const lines = [...state.lines];
                const currentLineText = lines[state.cursorRow] || '';
                const codePoints = [...currentLineText];
                codePoints.splice(state.cursorCol - 1, 1);
                lines[state.cursorRow] = codePoints.join('');
                return {
                    ...nextState,
                    lines,
                    cursorCol: state.cursorCol - 1,
                    preferredCol: null,
                };
            }
            else if (state.cursorRow > 0) {
                const nextState = pushUndo(state);
                const lines = [...state.lines];
                const currentLineText = lines[state.cursorRow] || '';
                const prevLineText = lines[state.cursorRow - 1] || '';
                const newCursorCol = cpLen(prevLineText);
                lines[state.cursorRow - 1] = prevLineText + currentLineText;
                lines.splice(state.cursorRow, 1);
                return {
                    ...nextState,
                    lines,
                    cursorRow: state.cursorRow - 1,
                    cursorCol: newCursorCol,
                    preferredCol: null,
                };
            }
            return state;
        }
        case 'delete': {
            const currentLineText = currentLine(state.cursorRow);
            if (state.cursorCol < cpLen(currentLineText)) {
                const nextState = pushUndo(state);
                const lines = [...state.lines];
                const codePoints = [...currentLineText];
                codePoints.splice(state.cursorCol, 1);
                lines[state.cursorRow] = codePoints.join('');
                return {
                    ...nextState,
                    lines,
                    preferredCol: null,
                };
            }
            else if (state.cursorRow < state.lines.length - 1) {
                const nextState = pushUndo(state);
                const lines = [...state.lines];
                const nextLineText = lines[state.cursorRow + 1] || '';
                lines[state.cursorRow] = currentLineText + nextLineText;
                lines.splice(state.cursorRow + 1, 1);
                return {
                    ...nextState,
                    lines,
                    preferredCol: null,
                };
            }
            return state;
        }
        case 'move': {
            const { direction } = action.payload;
            let newRow = state.cursorRow;
            let newCol = state.cursorCol;
            let newPreferredCol = state.preferredCol;
            switch (direction) {
                case 'up':
                    if (newRow > 0) {
                        newRow--;
                        newCol = newPreferredCol ?? state.cursorCol;
                        newCol = Math.min(newCol, currentLineLen(newRow));
                        newPreferredCol = state.preferredCol ?? state.cursorCol;
                    }
                    break;
                case 'down':
                    if (newRow < state.lines.length - 1) {
                        newRow++;
                        newCol = newPreferredCol ?? state.cursorCol;
                        newCol = Math.min(newCol, currentLineLen(newRow));
                        newPreferredCol = state.preferredCol ?? state.cursorCol;
                    }
                    break;
                case 'left':
                    if (newCol > 0) {
                        newCol--;
                    }
                    else if (newRow > 0) {
                        newRow--;
                        newCol = currentLineLen(newRow);
                    }
                    newPreferredCol = null;
                    break;
                case 'right':
                    if (newCol < currentLineLen(newRow)) {
                        newCol++;
                    }
                    else if (newRow < state.lines.length - 1) {
                        newRow++;
                        newCol = 0;
                    }
                    newPreferredCol = null;
                    break;
                case 'home':
                    newCol = 0;
                    newPreferredCol = null;
                    break;
                case 'end':
                    newCol = currentLineLen(newRow);
                    newPreferredCol = null;
                    break;
            }
            return {
                ...state,
                cursorRow: newRow,
                cursorCol: newCol,
                preferredCol: newPreferredCol,
            };
        }
        case 'undo': {
            if (state.undoStack.length > 0) {
                const lastEntry = state.undoStack[state.undoStack.length - 1];
                const currentEntry = {
                    lines: [...state.lines],
                    cursorRow: state.cursorRow,
                    cursorCol: state.cursorCol,
                };
                return {
                    ...state,
                    lines: [...lastEntry.lines],
                    cursorRow: lastEntry.cursorRow,
                    cursorCol: lastEntry.cursorCol,
                    preferredCol: null,
                    undoStack: state.undoStack.slice(0, -1),
                    redoStack: [...state.redoStack, currentEntry],
                };
            }
            return state;
        }
        case 'redo': {
            if (state.redoStack.length > 0) {
                const nextEntry = state.redoStack[state.redoStack.length - 1];
                const currentEntry = {
                    lines: [...state.lines],
                    cursorRow: state.cursorRow,
                    cursorCol: state.cursorCol,
                };
                return {
                    ...state,
                    lines: [...nextEntry.lines],
                    cursorRow: nextEntry.cursorRow,
                    cursorCol: nextEntry.cursorCol,
                    preferredCol: null,
                    undoStack: [...state.undoStack, currentEntry],
                    redoStack: state.redoStack.slice(0, -1),
                };
            }
            return state;
        }
        default:
            return state;
    }
}
/**
 * useTextBuffer hook
 */
export function useTextBuffer({ initialText = '', initialCursorOffset = 0, viewport, stdin, setRawMode, onChange, isValidPath, shellModeActive = false, }) {
    const initialState = useMemo(() => {
        const lines = initialText.split('\n');
        const [initialCursorRow, initialCursorCol] = calculateInitialCursorPosition(lines.length === 0 ? [''] : lines, initialCursorOffset);
        return {
            lines: lines.length === 0 ? [''] : lines,
            cursorRow: initialCursorRow,
            cursorCol: initialCursorCol,
            preferredCol: null,
            undoStack: [],
            redoStack: [],
            clipboard: null,
            selectionAnchor: null,
            viewportWidth: viewport.width,
        };
    }, [initialText, initialCursorOffset, viewport.width]);
    const [state, dispatch] = useReducer(textBufferReducer, initialState);
    const { lines, cursorRow, cursorCol, preferredCol, selectionAnchor } = state;
    // Calculate text content
    const text = useMemo(() => lines.join('\n'), [lines]);
    // Notify onChange
    useEffect(() => {
        onChange?.(text);
    }, [text, onChange]);
    // Visual representation (simplified for now)
    const visualLines = useMemo(() => lines, [lines]);
    const visualCursor = [cursorRow, cursorCol];
    const visualScrollRow = Math.max(0, cursorRow - Math.floor(viewport.height / 2));
    const renderedVisualLines = visualLines.slice(visualScrollRow, visualScrollRow + viewport.height);
    // Actions
    const setText = useCallback((newText) => {
        dispatch({ type: 'set_text', payload: newText });
    }, []);
    const insert = useCallback((insertText) => {
        dispatch({ type: 'insert', payload: insertText });
    }, []);
    const newline = useCallback(() => {
        dispatch({ type: 'newline' });
    }, []);
    const backspace = useCallback(() => {
        dispatch({ type: 'backspace' });
    }, []);
    const del = useCallback(() => {
        dispatch({ type: 'delete' });
    }, []);
    const move = useCallback((direction) => {
        dispatch({ type: 'move', payload: { direction } });
    }, []);
    const undo = useCallback(() => {
        dispatch({ type: 'undo' });
    }, []);
    const redo = useCallback(() => {
        dispatch({ type: 'redo' });
    }, []);
    const replaceRange = useCallback((startRow, startCol, endRow, endCol, replacementText) => {
        dispatch({ type: 'replace_range', payload: { startRow, startCol, endRow, endCol, text: replacementText } });
    }, []);
    const replaceRangeByOffset = useCallback((startOffset, endOffset, replacementText) => {
        const [startRow, startCol] = offsetToLogicalPos(text, startOffset);
        const [endRow, endCol] = offsetToLogicalPos(text, endOffset);
        replaceRange(startRow, startCol, endRow, endCol, replacementText);
    }, [text, replaceRange]);
    const moveToOffset = useCallback((offset) => {
        dispatch({ type: 'move_to_offset', payload: { offset } });
    }, []);
    const deleteWordLeft = useCallback(() => {
        dispatch({ type: 'delete_word_left' });
    }, []);
    const deleteWordRight = useCallback(() => {
        dispatch({ type: 'delete_word_right' });
    }, []);
    const killLineRight = useCallback(() => {
        dispatch({ type: 'kill_line_right' });
    }, []);
    const killLineLeft = useCallback(() => {
        dispatch({ type: 'kill_line_left' });
    }, []);
    const handleInput = useCallback((input, key) => {
        // Handle backspace with multiple detection methods
        if (key.backspace || key.name === 'backspace' || input === '\b' || input === '\x7f' || input === '\u007f') {
            backspace();
            return;
        }
        // Handle delete key
        if (key.delete || key.name === 'delete') {
            del();
            return;
        }
        if (key.ctrl) {
            switch (input) {
                case 'z':
                    undo();
                    break;
                case 'y':
                    redo();
                    break;
                case 'a':
                    move('home');
                    break;
                case 'e':
                    move('end');
                    break;
                case 'w':
                    deleteWordLeft();
                    break;
                case 'k':
                    killLineRight();
                    break;
                case 'u':
                    killLineLeft();
                    break;
                case 'h': // Ctrl+H is also backspace
                    backspace();
                    break;
            }
            return;
        }
        if (key.upArrow || key.name === 'up') {
            move('up');
        }
        else if (key.downArrow || key.name === 'down') {
            move('down');
        }
        else if (key.leftArrow || key.name === 'left') {
            move('left');
        }
        else if (key.rightArrow || key.name === 'right') {
            move('right');
        }
        else if (key.return || key.name === 'return' || key.name === 'enter') {
            newline();
        }
        else if (key.home || key.name === 'home') {
            move('home');
        }
        else if (key.end || key.name === 'end') {
            move('end');
        }
        else if (input && !key.ctrl && !key.meta && input.length > 0) {
            // Filter out control characters except printable ones
            if (input.charCodeAt(0) >= 32 || input === '\t') {
                insert(input);
            }
        }
    }, [undo, redo, move, deleteWordLeft, killLineRight, killLineLeft, newline, backspace, del, insert]);
    const openInExternalEditor = useCallback(() => {
        if (!stdin || !setRawMode)
            return;
        const tmpDir = fs.mkdtempSync(path.join(os.tmpdir(), 's647-'));
        const filePath = path.join(tmpDir, 'buffer.txt');
        try {
            fs.writeFileSync(filePath, text);
            const editor = process.env.EDITOR || (process.platform === 'win32' ? 'notepad' : 'nano');
            const wasRaw = stdin.isRaw ?? false;
            try {
                setRawMode(false);
                const { status, error } = spawnSync(editor, [filePath], {
                    stdio: 'inherit',
                });
                if (error)
                    throw error;
                if (typeof status === 'number' && status !== 0) {
                    throw new Error(`External editor exited with status ${status}`);
                }
                let newText = fs.readFileSync(filePath, 'utf8');
                newText = newText.replace(/\r\n?/g, '\n');
                dispatch({ type: 'set_text', payload: newText, pushToUndo: false });
            }
            catch (err) {
                console.error('[useTextBuffer] external editor error', err);
            }
            finally {
                if (wasRaw)
                    setRawMode(true);
                try {
                    fs.unlinkSync(filePath);
                }
                catch {
                    /* ignore */
                }
                try {
                    fs.rmdirSync(tmpDir);
                }
                catch {
                    /* ignore */
                }
            }
        }
        catch (err) {
            console.error('[useTextBuffer] failed to create temp file', err);
        }
    }, [text, stdin, setRawMode]);
    return {
        lines,
        text,
        cursor: [cursorRow, cursorCol],
        preferredCol,
        selectionAnchor,
        allVisualLines: visualLines,
        viewportVisualLines: renderedVisualLines,
        visualCursor,
        visualScrollRow,
        setText,
        insert,
        newline,
        backspace,
        del,
        move,
        undo,
        redo,
        replaceRange,
        replaceRangeByOffset,
        moveToOffset,
        deleteWordLeft,
        deleteWordRight,
        killLineRight,
        killLineLeft,
        handleInput,
        openInExternalEditor,
    };
}
//# sourceMappingURL=text-buffer.js.map